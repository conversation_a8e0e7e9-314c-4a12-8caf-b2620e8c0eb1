<div class="zen-holiday__info-bar">
	<div class="container">
		<div class="row row-cols-2 row-cols-lg-6 g-3 g-lg-0">
			
		<?php $holGrade = strtolower(preg_replace("/[^\w]+/", "-", $actLevel['activity-level'])); ?>
		<?php if ($holGrade) : ?>
			<div class="col zen-custom-justify d-flex align-items-center">
						<img class="grade-image no-lazy me-3 img-fluid"
								 src="/templates/zenbase/images/grading/<?= $holGrade; ?>.webp"
								 alt="<?= $actLevel['activity-level']; ?>"
								 height="40">
						<span class="zen-text zen-text--text-lg me-0 me-md-5 me-lg-1">
							<?php echo $actLevel['activity-level']; ?>
						</span>
			</div>
		<?php endif; ?>


		<?php if ($altRange['altitude-range']) : ?>
			<div class="col d-flex align-items-center ps-0 ps-sm-3">
				<i class="zen-icon zen-icon--default-lighter fontello icon-ascent me-3"></i>
						<span class="zen-text zen-text--text-lg me-1">
            	<?php echo $altRange['altitude-range']; ?>
            </span>
			</div>	
		<?php endif; ?>

			<div class="col zen-custom-justify d-flex align-items-center">
				<i class="zen-icon zen-icon--default-lighter fontello icon-calendar me-3"></i>
    		<?php $thisDuration = ($holidayDuration == 1) 
    			? JText::_('ZEN_HOLIDAY_SINGLE_DAY') 
    			: JText::_('ZEN_HOLIDAY_DAYS'); ?>

          <?php $numberOfNights = $holidayDuration + 1;?>
    		<span class="zen-text zen-text--text-lg me-0 me-md-5 me-lg-1">
      		<?php echo $numberOfNights." ".$thisDuration; ?>
      	</span>
			</div>
			<div class="col d-flex align-items-center ps-0 ps-sm-3">
				<i class="zen-icon zen-icon--default-lighter fontello icon-tag me-3"></i>
        <span class="zen-text zen-text--text-lg me-1">
        	<?php echo JText::_('ZEN_HOLIDAY_FROM')." "; ?>
        	<?php echo $fromPrice->currency_symbol.number_format($fromPrice->value).JText::_('ZEN_HOLIDAY_PRICING_PP'); ?>
        </span>
			</div>
			<div class="col-12 col-lg-2 col-xl-3 align-self-end position-relative order-first order-lg-last">
				<div class="zen-holiday__info-bar-image d-none">
			<?php if($holGuide) : ?>
				<?php foreach ($holGuide as $guide): ?>
					<?php foreach ($guide->items as $item): ?>
						<img 
							src="https://i.assetzen.net/i/<?php echo $item->images[0]->file_name; ?>/w:222/h:278/q:80.webp"
							class="text-center img-fluid" 
						/>
					<?php endforeach;?>
				<?php endforeach;?>
			<?php endif; ?>
				</div>
			</div>
		</div>
	</div>
</div>
<div class="zen-holiday__cta-bar">
	<div class="container">
		<div class="row g-3 g-lg-0">
			<div class="col d-flex pe-0 pe-sm-3 justify-content-end" >
			<?php if($holGuide) : ?>
				<?php foreach ($holGuide as $guide): ?>
					<?php foreach ($guide->items as $item): ?>
						<?php
						// Check if content contains a valid URL, otherwise use default
						$customUrl = trim(strip_tags($item->content));
						$isValidUrl = !empty($customUrl) && (
							strpos($customUrl, 'http://') === 0 ||
							strpos($customUrl, 'https://') === 0 ||
							strpos($customUrl, '/') === 0
						);
						$downloadUrl = $isValidUrl ? $customUrl : '/brochure-download';
						$fullUrl = $downloadUrl . '?automation=' . $item->xreference . '&subject=' . urlencode($name);
						?>
				<a href="<?= $fullUrl; ?>" class="zen-btn zen-btn--yellow zen-btn--full-size text-center ms-0 ms-lg-2">
					<?php echo JText::_('ZEN_HOLIDAY_GUIDE_TEXT'); ?>
				</a>
				<?php endforeach;?>
				<?php endforeach;?>
			<?php endif; ?>

        <button class="zen-btn zen-btn--secondary zen-btn--full-size ms-2 me-3 me-lg-0 justify-content-center " id="pricesDesktop">
					<span>
					  <?php echo JText::_('ZEN_SITE_BOOKING_TEXT'); ?> 
					</span>
				</button>
			</div>
		</div>
	</div>
</div>

<script>
	document.addEventListener('DOMContentLoaded', function(){ 
      
		window.isMobile = function() {
			let check = false;
			(function(a){if(/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(a)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(a.substr(0,4))) check = true;})(navigator.userAgent||navigator.vendor||window.opera);
			return check;
        };

		var bookNowButton = document.querySelector('#pricesDesktop'); 
		var datesTab = document.querySelector('#dates-prices-tab');
      	var datesAccordionSection = document.querySelector('[data-bs-target="#dates-prices-tab-content"]');
        var datesContent = document.querySelector('#dates-prices-tab-content');
		var accordion = document.querySelector('#dates-pricing-accordion');
		var tabTrigger = new bootstrap.Tab(datesTab);
      	var element;

		// On Click trigger the dates and prices tab for mobile and desktop
      
		bookNowButton.addEventListener('click', function() {
          	console.log(window.isMobile());
			tabTrigger.show();
          	if (window.isMobile()) {
              element = datesAccordionSection;
            } else {
              element = datesTab;
            }
          	var topOffset = element.getBoundingClientRect().top + window.pageYOffset-50;
          	window.scrollTo({
              top: topOffset,
              behavior: "smooth"
          	});
		});

	}, false);

</script>