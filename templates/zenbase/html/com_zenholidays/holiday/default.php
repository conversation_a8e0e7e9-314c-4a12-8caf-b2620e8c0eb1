<?php
	defined('_JEXEC') or die;
	include_once (JPATH_BASE . '/templates/zenbase/custom_helpers.php');
	jimport('mrzen.classes.utils');
	jimport('mrzen.helpers.ZenUGCHelper');

	// Import Joomla URI class
	use Joom<PERSON>\CMS\Uri\Uri as JUri;

  // Load SPPB CSS manually to avoid order issues.
  //
  JLoader::register('SppagebuilderHelperSite', JPATH_SITE . '/components/com_sppagebuilder/helpers/helper.php');
  SppagebuilderHelperSite::addStylesheet('sppagebuilder.css');

	JModelLegacy::addIncludePath(JPATH_ROOT . '/components/com_zenholidays/models');

	$app = JFactory::getApplication();
	$doc = JFactory::getDocument();
	$params = &$app->getParams();
	$prices = [];
	$relatedTrips = [];
	$holExtras = false;
	$versions = false;

	$templateParams = $app->getTemplate(true)->params;



	// Related holiday feature.
	//
	$holModel = JModelLegacy::getInstance('Holiday', 'ZenHolidaysModel');
	$this->item->relations = $holModel->getRelations((int)$this->item->id);
	foreach ($this->item->relations as &$relation) {
		$relation->holiday = $holModel->getItem($relation->right_holiday_id);
			array_push($relatedTrips, $relation->holiday);
	}
	// Set versions.
	//
	if (count($this->item->versions) > 1) {
		$versions = true;
	}
	// Standard main image management.
	//
	if ($this->item->images) {
		$total_images = count($this->item->images);
		foreach ($this->item->images as $key => $image) {
			if ($image->link_featured) {
				$this->item->featured_images[] = $image;
			}
		}
		if (!$this->item->featured_images) {
			$this->item->featured_images = array_slice($this->item->images, 0, 1);
		}
	}
	// Standard category management.
	//
	foreach ($this->item->categories as $key => $category) {
		if ($category->parent->alias == 'altitude-range') {
			$altRange[$category->parent->alias] = $category->title;
		}
		if ($category->parent->alias == 'activity-level') {
			$actLevel[$category->parent->alias] = $category->title;
		}
	}
	// Standard dates and prices.
	//
	foreach ($this->item->dates as $d) {
		foreach ($d->prices as $p) {
			array_push($prices, $p);
		}
	}
  // Tab headings.
	//
	$tab01_raw = JText::_('COM_ZENHOLIDAYS_OVERVIEW_TAB_LABEL');
	$tab01_display = $tab01_raw;
	$tab01 = str_replace(['<br>', ' & '], ['', ' and '], $tab01_raw);

	$tab02_raw = JText::_('COM_ZENHOLIDAYS_ITINERARY_TAB_LABEL');
	$tab02_display = $tab02_raw;
	$tab02 = str_replace(['<br>', ' & '], ['', ' and '], $tab02_raw);

	$tab03_raw = JText::_('COM_ZENHOLIDAYS_DATES_PRICES_TAB_LABEL');
	$tab03_display = $tab03_raw;
	$tab03 = str_replace(['<br>', ' & ', '&amp;'], ['', ' and ', ' and '], $tab03_raw);

	$tab04_raw = JText::_('COM_ZENHOLIDAYS_EXTENSIONS_TAB_LABEL');
	$tab04_display = $tab04_raw;
	$tab04 = str_replace(['<br>', ' & '], ['', ' and '], $tab04_raw);

	$tab05_raw = JText::_('COM_ZENHOLIDAYS_VIDEOS_TAB_LABEL');
	$tab05_display = $tab05_raw;
	$tab05 = str_replace(['<br>', ' & '], ['', ' and '], $tab05_raw);

	$tab06_raw = JText::_('COM_ZENHOLIDAYS_FAQ_TAB_LABEL');
	$tab06_display = $tab06_raw;
	$tab06 = str_replace(['<br>', ' & '], ['', ' and '], $tab06_raw);

	$tab07_raw = JText::_('COM_ZENHOLIDAYS_TAB_REVIEWS');
	$tab07_display = $tab07_raw;
	$tab07 = str_replace(['<br>', ' & '], ['', ' and '], $tab07_raw);

	$tab08_raw = JText::_('COM_ZENHOLIDAYS_FOOD_ACCOMMODATION_TAB_LABEL');
	$tab08_display = $tab08_raw;
	$tab08 = str_replace(['<br>', ' & '], ['', ' and '], $tab08_raw);
  // Variables.
  //
  $id = $this->item->id;
 	$name = $this->item->name;
	$title = $this->item->title;
	$dates = ZenbaseCustomHelpers::organiseDatesByYear($this->item->dates);
	$relations = $this->item->relations;
	$fromPrice = $this->item->from_price->price;
	$featImages = $this->item->featured_images;
	$rootImage = $featImages[0];
	$holidayIntro = $this->item->intro;
	$holidayImages = $this->item->images;
	$holidayExtras = $this->item->activities;
	$holidayDuration = $this->item->selected_version->number_of_days - 1;
	$holidayItinerary = $this->item->selected_version->itinerary;
	$holidayDescription = $this->item->description;
	$bookingEnabled = (int) $templateParams->get('bookingenabled', 0);
	$bookingDisabledRedirect = $templateParams->get('bookingdisabledredirect', '');
	$bookingPrefix = $templateParams->get('bookingprefix', '');
	// Copy items.
	//
	$holUsp = ZenModelHelper::getCopyItemsByAlias('com_zenholidays', $id, 'usp');
	$holGuide = ZenModelHelper::getCopyItemsByAlias('com_zenholidays', $id, 'guide');
	$holFAQ = ZenModelHelper::getCopyItemsByAlias('com_zenholidays', $id, 'faq');
	$holMoreInfo = ZenModelHelper::getCopyItemsByAlias('com_zenholidays', $id, 'more-info');
	$included = ZenModelHelper::getCopyItemsByAlias('com_zenholidays', $id, 'included');
	$excluded = ZenModelHelper::getCopyItemsByAlias('com_zenholidays', $id, 'excluded');
	$highlights = ZenModelHelper::getCopyItemsByAlias('com_zenholidays', $id, 'highlights');
	$essentialKit = ZenModelHelper::getCopyItemsByAlias('com_zenholidays', $id, 'essential-kit');
	$howChallenging = ZenModelHelper::getCopyItemsByAlias('com_zenholidays', $id, 'how-challenging');

	// UGC
	//
	$ugcReviews = ZenUGCHelper::getUGCItems('com_zenholidays', $id, 'reviews');
	foreach ($ugcReviews[0]->items as $item) {
		array_push($ratingResults, $item->xreference);
	}
	$ratingAverage = array_sum($ratingResults) / count($ratingResults);
	$ratingCount = count($ratingResults);
    $paymentPanel = ZenModelHelper::getCopyItemsByAlias('com_zenholidays', $id, 'monthly-payment-panel');
  // Manually load breadcrumbs.
  //
  $breadcrumbs_mod = JModuleHelper::getModule('mod_breadcrumbs', '');
  $breadcrumbs_modParams = new JRegistry($breadcrumbs_mod->params);
  $breadcrumbs_modParams->set('style', '');
  $breadcrumbs_mod->showtitle = '0';
  $breadcrumbs_modRender = $doc->loadRenderer('module');
  $breadcrumbs_modHtml = $breadcrumbs_modRender->render($breadcrumbs_mod, array('params'=> $breadcrumbs_modParams));
  // Preload main image.
  //
  if ($rootImage) {
		$mainHolImage = JHtmlImages::getCachedImageUrl($rootImage, 1920, 600);
	  $mainHolImgPreload = '<link href="'.$mainHolImage.'" rel="preload" as="image">';
	  $doc->addCustomTag($mainHolImgPreload);
  }

  // Keep only the class fix script which is required for functionality
  // The debug panels have been turned off
  $doc->addScript(JUri::root(true) . '/templates/zenbase/js/deep-link-class-fix.js');

  /* Debug scripts - commented out
  $doc->addScript(JUri::root(true) . '/templates/zenbase/js/deep-link-test.js');
  $doc->addScript(JUri::root(true) . '/templates/zenbase/js/deep-link-environment-debug.js');
  $doc->addScript(JUri::root(true) . '/templates/zenbase/js/deep-link-diagnostics.js');
  */
  // Statements.
  //
	if (count($featImages) > 0) {
		$breadcrumbsClass = "light-breadcrumbs";
		$titlePosition = "zen-hero__absolute-title";
		$titleClass = "zen-title zen-title--hero zen-title--light";
		$heroClass = "p-0";
	}
	else {
		$breadcrumbsClass = "regular-breadcrumbs";
		$titlePosition = "zen-hero__relative-title";
		$titleClass = "zen-title zen-title--hero text-center py-5";
		$heroClass = "mb-5";
	}
	// Window Width Class Management.
	//
	if ($_COOKIE["winWidth"] >= 992) {
		$contentClass = "tab-pane fade";
		$setActive = "active show";
	}
	else {
		$contentClass = "collapse";
		$setActive = "";
	}

// Include global difficulty modals at root level
require(JPATH_BASE . '/templates/zenbase/html/partials/global_difficulty_modals.php');

// Add the difficulty modal handler script
use Joomla\CMS\Factory;
use Joomla\CMS\Uri\Uri;
$doc = Factory::getDocument();
$doc->addScript(Uri::base(true) . '/templates/zenbase/js/difficulty-modal-handler.js');
?>
<!------------------------------------------------------------------------------
// Manually Load Breadcrumbs
//----------------------------------------------------------------------------->
<div class="zen-body__breadcrumbs">
  <div class="container-fluid">
    <div class="row">
      <div class="col-12">
        <div class="moduletable <?= $breadcrumbsClass; ?>">
          <?php echo $breadcrumbs_modHtml; ?>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="zen-holiday">
  <!------------------------------------------------------------------------------
  // Top Module Position
  //----------------------------------------------------------------------------->
	<?php if ($doc->getBuffer('modules', 'zenHolidayTop')) : ?>
		<div class="zen-holiday__content">
    	<?php echo $doc->getBuffer('modules', 'zenHolidayTop', array('style' => 'xhtml')); ?>
    </div>
  <?php endif; ?>
  <!------------------------------------------------------------------------------
  // Hero Area
  //----------------------------------------------------------------------------->
	<?php include('default_hero.php'); ?>
  <!------------------------------------------------------------------------------
  // Intro Text
  //----------------------------------------------------------------------------->
  <div class="zen-holiday__intro">
    <div class="container">
      <div class="row justify-content-center">
        <div class="col-12">
          <div class="zen-text--text-lg intro text-left text-md-center">
            <?php echo $holidayIntro; ?>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!------------------------------------------------------------------------------
  // Trip Guide
  //----------------------------------------------------------------------------->
  <?php if($holGuide) : ?>
  <div class="zen-holiday__guide">
    <div class="container">
      <div class="row justify-content-center">
        <div class="col-12">
          <div class="zen-media d-block d-md-flex align-items-center">
            <div class="zen-media__image-container flex-shrink-0 me-md-4 h-100">
              <div class="zen-media__image">
                <img class="" src="https://i.assetzen.net/i/F7T9GHkRZZAy/w:200/q:90.webp" alt="Download the full Trip Guide">
              </div>
            </div>
            <div class="zen-media__text flex-grow-1">
              <div class="zen-media__title">
                <h3>
                  Download the full Trip Guide
                </h3>
              </div>
              <div class="zen-media__content">
                <p>Find out all of the awesome details about the trip and download the full guide and walkthrough right here.</p>
              </div>
              <?php foreach ($holGuide as $guide): ?>
                <?php foreach ($guide->items as $item): ?>
                  <?php
                  // Check if content contains a URL, otherwise use default
                  $customUrl = trim(strip_tags($item->content));
                  $downloadUrl = !empty($customUrl) ? $customUrl : '/brochure-download';
                  $fullUrl = $downloadUrl . '?automation=' . $item->xreference . '&subject=' . urlencode($name);
                  ?>
                  <div class="mobile-download-btn d-md-none">
                    <a href="<?= $fullUrl; ?>" class="btn btn-primary" target="_blank" rel="noopener noreferrer">
                      Download guide
                      <img src="/templates/zenbase/icons/download.svg" alt="Download" width="24" height="24">
                    </a>
                  </div>
                <?php endforeach;?>
              <?php endforeach;?>
            </div>
            <?php foreach ($holGuide as $guide): ?>
              <?php foreach ($guide->items as $item): ?>
                <?php
                // Check if content contains a URL, otherwise use default
                $customUrl = trim(strip_tags($item->content));
                $downloadUrl = !empty($customUrl) ? $customUrl : '/brochure-download';
                $fullUrl = $downloadUrl . '?automation=' . $item->xreference . '&subject=' . urlencode($name);
                ?>
                <div class="flex-shrink-0 download-btn d-none d-md-block">
                  <a href="<?= $fullUrl; ?>" class="btn btn-primary" target="_blank" rel="noopener noreferrer">
                    Download guide
                    <img src="/templates/zenbase/icons/download.svg" alt="Download" width="24" height="24">
                  </a>
                </div>
                <a class="stretched-link" href="<?= $fullUrl; ?>" target="_blank" rel="noopener noreferrer"></a>
              <?php endforeach;?>
            <?php endforeach;?>
          </div>
        </div>
      </div>
    </div>
  </div>
  <?php endif; ?>

  <!------------------------------------------------------------------------------
  // Content Tabs
  //----------------------------------------------------------------------------->
  <?php include('default_tabs.php'); ?>
	<!------------------------------------------------------------------------------
  // Main Content Area
  //----------------------------------------------------------------------------->
	<div id="holidayContent" class="zen-holiday__content">
    <div class="tab-content" id="holTabContent">
      <!------------------------------------------------------------------------------
						// Overview
						//----------------------------------------------------------------------------->
						<?php include JPATH_THEMES . '/zenbase/html/com_zenholidays/holiday/default_overview.php'; ?>
        		<!------------------------------------------------------------------------------
						// Itinerary
						//----------------------------------------------------------------------------->
						<?php if ($holidayItinerary): ?>
							<?php include('default_itinerary.php'); ?>
						<?php endif; ?>
        		<!------------------------------------------------------------------------------
						// Dates and Prices
						//----------------------------------------------------------------------------->
						<?php include('default_dates-prices.php'); ?>
        		<!------------------------------------------------------------------------------
						// Food & Accommodation
						//----------------------------------------------------------------------------->
						<?php include JPATH_THEMES . '/zenbase/html/com_zenholidays/holiday/default_food-accommodation.php'; ?>
        		<!------------------------------------------------------------------------------
						// Trip Extensions
						//----------------------------------------------------------------------------->
						<?php if ($hasExtensions): ?>
							<?php include('default_extensions.php'); ?>
						<?php endif; ?>

        		<!------------------------------------------------------------------------------
						// FAQ
						//----------------------------------------------------------------------------->
						<?php if ($holFAQ): ?>
							<?php include('default_faq.php'); ?>
						<?php endif; ?>

        		<!------------------------------------------------------------------------------
						// Blog
						//----------------------------------------------------------------------------->
						<?php include JPATH_THEMES . '/zenbase/html/com_zenholidays/holiday/default_blog.php'; ?>
        		<!------------------------------------------------------------------------------
						// Contact
						//----------------------------------------------------------------------------->
						<?php include JPATH_THEMES . '/zenbase/html/com_zenholidays/holiday/default_contact.php'; ?>
					</div>
    </div>
		<!------------------------------------------------------------------------------
		// Related Trips
		//----------------------------------------------------------------------------->
		<?php if ($relatedTrips): ?>
			<?php /* include('default_relations.php'); */ ?>
		<?php endif; ?>
	</div>
	<!------------------------------------------------------------------------------
  // Bottom Module Position
  //----------------------------------------------------------------------------->
	<?php if ($doc->getBuffer('modules', 'zenHolidayBottom')) : ?>
		<div class="zen-holiday__content">
    	<?php echo $doc->getBuffer('modules', 'zenHolidayBottom', array('style' => 'xhtml')); ?>
    </div>
  <?php endif; ?>
</div>
<?php if (count($holidayImages) > 1) : ?>
	<?php $modalGallery = true; ?>
	<?php include(JPATH_BASE . '/templates/zenbase/html/partials/modal.php'); ?>
<?php endif; ?>
<style>
  .zen-holiday__content-box {
    background: #F2F2F2;
    box-shadow: none;
  }
  .zen-holiday__tabs {
    background-color: white;
    padding: 0;
    box-shadow: none;
    border-top: 1px solid #909090;
    border-bottom: 1px solid #909090;
    display: none;  /* Hide by default */
  }

  .zen-holiday__tabs .nav-link span {
    text-overflow: ellipsis;
    overflow: hidden;
  }

  /* Container styles */
  /* .zen-holiday .container {
    max-width: 1262px;
    margin: 0 auto;
  } */

  @media (min-width: 992px) {
    .zen-holiday__tabs {
        display: block;  /* Show only on desktop */
    }
  }

  .zen-tab__container {
    display: flex;
    flex-wrap: nowrap;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    -ms-overflow-style: none;
    scrollbar-width: none;
    justify-content: center;
  }

  .zen-tab__container::-webkit-scrollbar {
    display: none;
  }

  .zen-tab__heading-item {
    margin-bottom: 0;
    background: transparent;
    min-width: 162px;
    max-width: 199px;
    width: auto;
    flex: 0 1 auto;
  }

  .zen-tab__heading-item .nav-link {
    text-transform: none;
    position: relative;
    white-space: normal;
    font-size: 14px;
    font-weight: 500;
    line-height: normal;
    border-right: 1px solid #909090;
    border-left: 0;
    transition: none;
    text-decoration: none;
    color: inherit;
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 9px;
    padding: 16px 20px;
    height: 68px;
  }

  .zen-tab__heading-item:last-child .nav-link {
    border-right: 0;
  }

  .zen-tab__heading-item .nav-link:before {
    content: '';
    display: block;
    width: 40px;
    height: 40px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    flex-shrink: 0;
  }

  /* Tab-specific icons */
  #trip-overview-tab:before {
    background-image: url('/templates/zenbase/icons/trips/overview.svg');
  }

  #itinerary-tab:before {
    background-image: url('/templates/zenbase/icons/trips/itinerary.svg');
  }

  #dates-prices-tab:before {
    background-image: url('/templates/zenbase/icons/trips/dates_prices.svg');
  }

  #food-accommodation-tab:before {
    background-image: url('/templates/zenbase/icons/trips/food_accomm.svg');
  }

  #trip-extensions-tab:before {
    background-image: url('/templates/zenbase/icons/trips/extensions.svg');
  }

  #faqs-tab:before {
    background-image: url('/templates/zenbase/icons/trips/faqs.svg');
  }

  .zen-tab__heading-item .nav-link.active {
    border-bottom: 9px solid #fe7720;
    border-left: 0;
    padding-bottom: 7px;
  }

  .zen-holiday__tabs .tab-content {
    padding: 20px 0;
  }

  .zen-holiday__tabs .tab-content > .tab-pane {
    padding: 0;
  }

  @media screen and (max-width: 576px) {
    .zen-tab__heading-item .nav-link:not(.active) {
      opacity: 0.7;
    }
  }

  .zen-holiday {
    background-color: white;
  }

  .zen-holiday__intro {
    padding: 30px 0;
  }

  .zen-holiday__intro .intro {
    font-weight: 600;
  }

  .zen-holiday__guide .zen-media {
    background-color: #fe7720;
    border-radius: 10px;
    border: none;
    padding: 16px !important;
    margin-bottom: 50px;
    position: relative;
  }

  /* Add mobile styles for the guide section */
  @media (max-width: 767px) {
    .zen-holiday__guide .zen-media {
        display: grid !important;
        grid-template-columns: 33% 66%;
        gap: 1rem;
        padding: 1rem !important;
    }

    .zen-holiday__guide .zen-media__image-container {
      /* width: 33.3%; */
      align-items: center;
      display: flex;
    }
    .zen-holiday__guide .zen-media__text .mobile-download-btn a {
      /* width: 66.6%; */
      align-self: flex-start;
    }

    .zen-holiday__guide .zen-media__image {
        width: 100%;
        height: auto;
        align-items: center;
    }

    .zen-holiday__guide .zen-media__image img {
        width: 118%;
        height: auto;
        object-fit: contain;
    }

    .zen-holiday__guide .zen-media__title h3 {
        text-align: left;
        font-size:24px;
        line-height: 26px;
        font-weight: 800;
        margin-bottom: 5px;
        padding: 0 20px 0 0;
    }

    .zen-holiday__guide .zen-media__content {
        text-align: left;
        margin-bottom: .5rem;
    }

    .zen-holiday__guide .flex-grow-1 {
        display: flex;
        flex-direction: column;
    }

    .zen-holiday__guide .mobile-download-btn {
        display: flex;
    }

    .zen-holiday__guide .mobile-download-btn a {
      margin-left: 0!important;
      margin-right: 0!important;
    }

    .zen-holiday__guide .mobile-download-btn .btn {
        display: inline-flex;
        align-items: center;
        background: white;
        color: #000;
        padding: 0.5rem 1rem;
        border-radius: 100px;
        font-size: 1rem;
        text-decoration: none;
        white-space: nowrap;
        gap: 0;
    }

    .zen-holiday__guide .mobile-download-btn .btn img {
        margin-left: 0.5rem;
        filter: brightness(0);
    }

    .zen-holiday__guide .stretched-link {
        z-index: 1;
    }
  }

  .zen-holiday__guide .btn {
    white-space: nowrap;
  }

  /* Make download icon black on both mobile and desktop */
  .zen-holiday__guide .btn img {
    filter: brightness(0);
  }

</style>

<script>
// Deep link button handlers are now loaded from deep-link-button-handlers.js
// This script has been updated to fix the issue with buttons not working a second time
</script>